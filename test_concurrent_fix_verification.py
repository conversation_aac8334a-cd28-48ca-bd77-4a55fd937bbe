#!/usr/bin/env python3
"""
Quick test to verify that the concurrent LLM fix is working.
Tests if both main and backup LLMs are used simultaneously.
"""

import asyncio
import aiohttp
import json
import time
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

BASE_URL = "http://127.0.0.1:8000"

async def send_inference_request(session: aiohttp.ClientSession, user_id: int):
    """Send an inference request and track timing"""
    try:
        request_data = {
            "query": f"Hello, I am user {user_id}. Please count from 1 to 50 slowly and tell me about each number.",
            "collection_name": "test_collection",
            "config": {
                "max_new_tokens": 300,
                "temperature": 0.7
            },
            "language": "en"
        }
        
        start_time = time.time()
        logger.info(f"User {user_id}: Starting request at {start_time:.2f}")
        
        async with session.post(
            f"{BASE_URL}/api/v1/inference",
            json=request_data,
            timeout=aiohttp.ClientTimeout(total=120)
        ) as response:
            
            if response.status != 200:
                logger.error(f"User {user_id}: Error {response.status}")
                return
            
            connect_time = time.time()
            logger.info(f"User {user_id}: Connected at {connect_time:.2f} (after {connect_time - start_time:.2f}s)")
            
            first_token_time = None
            token_count = 0
            
            async for line in response.content:
                line_str = line.decode('utf-8').strip()
                if line_str.startswith('data: '):
                    try:
                        data = json.loads(line_str[6:])
                        
                        if 'token' in data:
                            token_count += 1
                            current_time = time.time()
                            
                            if first_token_time is None:
                                first_token_time = current_time
                                logger.info(f"User {user_id}: FIRST TOKEN at {current_time:.2f} (after {current_time - start_time:.2f}s)")
                            
                            # Log every 10th token to track progress
                            if token_count % 10 == 0:
                                logger.info(f"User {user_id}: Token #{token_count} at {current_time:.2f}")
                        
                        elif data.get('event') == 'eos':
                            end_time = time.time()
                            logger.info(f"User {user_id}: COMPLETED at {end_time:.2f} with {token_count} tokens (total time: {end_time - start_time:.2f}s)")
                            return
                            
                    except json.JSONDecodeError:
                        continue
            
    except Exception as e:
        logger.error(f"User {user_id}: Exception - {e}")

async def main():
    """Test concurrent processing with 4 requests"""
    logger.info("Testing concurrent LLM processing fix...")
    
    async with aiohttp.ClientSession() as session:
        # Send 4 requests simultaneously
        tasks = []
        for i in range(1, 5):
            task = send_inference_request(session, i)
            tasks.append(task)
        
        logger.info("Sending 4 requests simultaneously...")
        start_time = time.time()
        
        await asyncio.gather(*tasks)
        
        end_time = time.time()
        logger.info(f"All requests completed in {end_time - start_time:.2f} seconds")

if __name__ == "__main__":
    asyncio.run(main())
