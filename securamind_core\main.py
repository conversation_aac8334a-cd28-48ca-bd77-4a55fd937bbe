# securamind_core/main.py

def ensure_non_root_execution():
    """Ensure the application is not running with root/admin privileges"""
    import os
    import sys
    
    if os.name == 'nt':  # Windows
        import ctypes
        if ctypes.windll.shell32.IsUserAnAdmin() != 0:
            print("Error: Application must not run with administrator privileges", file=sys.stderr)
            sys.exit(1)
    else:  # Unix-like
        if os.geteuid() == 0:
            print("Error: Application must not run with root privileges", file=sys.stderr)
            sys.exit(1)

# Call this before any other code executes
ensure_non_root_execution()

import logging
import asyncio

from fastapi import FastAPI
from qdrant_client import QdrantClient

from .config import settings, setup_logging
from .ingestion import router as ingestion_router
from .inference import router as inference_router
from .llm_process_manager import llm_process_manager

# --- Initialize Logging ---
setup_logging(settings.log_level)
logger = logging.getLogger(__name__)

# --- Global instances ---
qdrant_client_instance: QdrantClient = None

# --- Lifespan Event Handler ---
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Why: Handle application startup and shutdown using modern FastAPI lifespan events.
    How: Starts LLM processes and initializes clients on startup, cleans up on shutdown.
    """
    global qdrant_client_instance

    # Startup
    logger.info("Mindend startup sequence initiated.")

    # Start LLM Process Manager
    logger.info("Starting LLM processes...")
    try:
        await llm_process_manager.start()
        logger.info("LLM processes started successfully.")
    except Exception as e:
        logger.error(f"Error starting LLM processes: {e}", exc_info=True)

    # Start centralized request queue
    logger.info("Starting centralized request queue...")
    try:
        await centralized_queue.start()
        logger.info("Centralized request queue started successfully.")
    except Exception as e:
        logger.error(f"Error starting centralized queue: {e}", exc_info=True)

    # Initialize Qdrant Client
    logger.info(f"Initializing Qdrant client with path: {settings.absolute_qdrant_path}")
    try:
        qdrant_client_instance = QdrantClient(path=str(settings.absolute_qdrant_path))
        logger.info("Qdrant client initialized successfully.")
    except Exception as e:
        logger.error(f"Error initializing Qdrant client: {e}", exc_info=True)

    # Start periodic cleanup task for ingestion progress
    from .ingestion import periodic_cleanup
    cleanup_task = asyncio.create_task(periodic_cleanup())
    logger.info("Started periodic ingestion progress cleanup task.")

    # Yield control to the application
    yield

    # Cancel cleanup task on shutdown
    cleanup_task.cancel()
    try:
        await cleanup_task
    except asyncio.CancelledError:
        logger.info("Periodic cleanup task cancelled.")
    except Exception as e:
        logger.error(f"Error cancelling cleanup task: {e}", exc_info=True)

    # Shutdown
    logger.info("Application shutdown sequence initiated.")

    # Stop centralized request queue
    try:
        await centralized_queue.stop()
        logger.info("Centralized request queue stopped.")
    except Exception as e:
        logger.error(f"Error stopping centralized queue: {e}", exc_info=True)

    # Stop LLM Process Manager
    try:
        await llm_process_manager.stop()
        logger.info("LLM processes stopped.")
    except Exception as e:
        logger.error(f"Error stopping LLM processes: {e}", exc_info=True)

    if qdrant_client_instance:
        try:
            qdrant_client_instance.close()
            logger.info("Qdrant client closed.")
        except Exception as e:
            logger.error(f"Error closing Qdrant client: {e}", exc_info=True)

# --- FastAPI App Initialization ---
app = FastAPI(
    title="SecuraMind Mindend",
    description="API for local LLM inference and data ingestion.",
    version="0.3.0",
    lifespan=lifespan
)

# Include routers
app.include_router(ingestion_router)
app.include_router(inference_router)

# Import and include status router
from .ingestion import status_router
app.include_router(status_router)

# --- Centralized Request Queue System ---
import asyncio
import time
from typing import Optional, Tuple, Any
from dataclasses import dataclass
import uuid

@dataclass
class QueuedRequest:
    """Represents a queued request waiting for processing"""
    request_id: str
    request_type: str  # "chat_completion" or "embedding"
    request_data: dict
    future: asyncio.Future
    created_at: float
    retry_count: int = 0
    max_retries: int = 3

class CentralizedRequestQueue:
    """Centralized queue that manages all LLM requests with automatic retry and recovery"""

    def __init__(self):
        self.chat_queue: asyncio.Queue = asyncio.Queue()
        self.embedding_queue: asyncio.Queue = asyncio.Queue()
        self.processing_task: Optional[asyncio.Task] = None
        self.running = False

    async def start(self):
        """Start the centralized request processor"""
        if self.running:
            return

        self.running = True
        self.processing_task = asyncio.create_task(self._process_requests())
        logger.info("Centralized request queue started")

    async def stop(self):
        """Stop the centralized request processor"""
        self.running = False
        if self.processing_task:
            self.processing_task.cancel()
            try:
                await self.processing_task
            except asyncio.CancelledError:
                pass
        logger.info("Centralized request queue stopped")

    async def submit_chat_request(self, request_data: dict) -> Any:
        """Submit a chat completion request and wait for result"""
        request_id = str(uuid.uuid4())
        future = asyncio.Future()

        queued_request = QueuedRequest(
            request_id=request_id,
            request_type="chat_completion",
            request_data=request_data,
            future=future,
            created_at=time.time()
        )

        await self.chat_queue.put(queued_request)
        logger.debug(f"Queued chat request {request_id}")

        # Wait for result
        return await future

    async def submit_embedding_request(self, request_data: dict) -> Any:
        """Submit an embedding request and wait for result"""
        request_id = str(uuid.uuid4())
        future = asyncio.Future()

        queued_request = QueuedRequest(
            request_id=request_id,
            request_type="embedding",
            request_data=request_data,
            future=future,
            created_at=time.time()
        )

        await self.embedding_queue.put(queued_request)
        logger.debug(f"Queued embedding request {request_id}")

        # Wait for result
        return await future

    async def _process_requests(self):
        """Main request processing loop"""
        while self.running:
            try:
                # Process chat requests and embedding requests concurrently
                await asyncio.gather(
                    self._process_chat_queue(),
                    self._process_embedding_queue(),
                    return_exceptions=True
                )
            except Exception as e:
                logger.error(f"Error in request processor: {e}", exc_info=True)
                await asyncio.sleep(1)

    async def _process_chat_queue(self):
        """Process chat completion requests"""
        while self.running:
            try:
                # Wait for a request with timeout
                try:
                    request = await asyncio.wait_for(self.chat_queue.get(), timeout=1.0)
                except asyncio.TimeoutError:
                    continue

                # Get best available LLM process
                selected_process, llm_type = await self._get_best_chat_process()

                if selected_process is None:
                    # No healthy processes - wait and retry
                    logger.warning(f"No healthy chat LLM processes, requeueing request {request.request_id}")
                    await asyncio.sleep(2)
                    await self.chat_queue.put(request)
                    continue

                # Process the request asynchronously (non-blocking for concurrent processing)
                asyncio.create_task(self._execute_chat_request(request, selected_process, llm_type))

            except Exception as e:
                logger.error(f"Error processing chat queue: {e}", exc_info=True)
                await asyncio.sleep(1)

    async def _process_embedding_queue(self):
        """Process embedding requests"""
        while self.running:
            try:
                # Wait for a request with timeout
                try:
                    request = await asyncio.wait_for(self.embedding_queue.get(), timeout=1.0)
                except asyncio.TimeoutError:
                    continue

                # Get embedding process
                embedding_process = llm_process_manager.get_process("embedding")

                if embedding_process is None or not embedding_process.is_healthy():
                    # No healthy embedding process - wait and retry
                    logger.warning(f"Embedding process unhealthy, requeueing request {request.request_id}")
                    await asyncio.sleep(2)
                    await self.embedding_queue.put(request)
                    continue

                # Process the request
                await self._execute_embedding_request(request, embedding_process)

            except Exception as e:
                logger.error(f"Error processing embedding queue: {e}", exc_info=True)
                await asyncio.sleep(1)

    async def _get_best_chat_process(self) -> Tuple[Optional[Any], Optional[str]]:
        """Get the best available chat LLM process with intelligent routing"""
        main_process = llm_process_manager.get_process("main")
        backup_process = llm_process_manager.get_process("backup")

        # Priority 1: Main LLM if available (healthy and not busy)
        if main_process and main_process.is_available():
            logger.debug("Routing to main LLM process (preferred)")
            return main_process, "main"

        # Priority 2: Backup LLM if available
        if backup_process and backup_process.is_available():
            logger.debug("Main LLM busy, routing to backup LLM process")
            return backup_process, "backup"

        # Priority 3: Queue on main LLM if healthy (even if busy)
        if main_process and main_process.is_healthy():
            logger.debug("Both LLMs busy, queueing on main LLM process")
            return main_process, "main"

        # Priority 4: Queue on backup LLM if healthy
        if backup_process and backup_process.is_healthy():
            logger.debug("Main LLM unhealthy, queueing on backup LLM process")
            return backup_process, "backup"

        # No healthy processes available
        logger.warning("No healthy chat LLM processes available")
        return None, None

    async def _execute_chat_request(self, request: QueuedRequest, process: Any, llm_type: str):
        """Execute a chat completion request with retry logic"""
        try:
            logger.debug(f"Processing chat request {request.request_id} on {llm_type} LLM")

            # Send request to LLM process (no timeout - direct routing ensures completion)
            response = await process.send_request(request.request_data)

            # Set successful result
            if not request.future.done():
                request.future.set_result(response)

        except Exception as e:
            logger.error(f"Error executing chat request {request.request_id} on {llm_type}: {e}")

            # Retry logic
            if request.retry_count < request.max_retries:
                request.retry_count += 1
                logger.info(f"Retrying chat request {request.request_id} (attempt {request.retry_count}/{request.max_retries})")
                await asyncio.sleep(2 ** request.retry_count)  # Exponential backoff
                await self.chat_queue.put(request)
            else:
                # Max retries exceeded - fail the request
                if not request.future.done():
                    request.future.set_exception(RuntimeError(f"Chat request failed after {request.max_retries} retries: {str(e)}"))

    async def _execute_embedding_request(self, request: QueuedRequest, process: Any):
        """Execute an embedding request with retry logic"""
        try:
            logger.debug(f"Processing embedding request {request.request_id}")

            # Send request to embedding process (no timeout - direct routing ensures completion)
            response = await process.send_request(request.request_data)

            # Set successful result
            if not request.future.done():
                request.future.set_result(response)

        except Exception as e:
            logger.error(f"Error executing embedding request {request.request_id}: {e}")

            # Retry logic
            if request.retry_count < request.max_retries:
                request.retry_count += 1
                logger.info(f"Retrying embedding request {request.request_id} (attempt {request.retry_count}/{request.max_retries})")
                await asyncio.sleep(2 ** request.retry_count)  # Exponential backoff
                await self.embedding_queue.put(request)
            else:
                # Max retries exceeded - fail the request
                if not request.future.done():
                    request.future.set_exception(RuntimeError(f"Embedding request failed after {request.max_retries} retries: {str(e)}"))

# Global centralized queue instance
centralized_queue = CentralizedRequestQueue()

# --- Process-Safe LLM Access Functions (Updated to use Centralized Queue) ---
async def safe_llm_create_chat_completion(messages, max_tokens, temperature):
    """
    Process-safe wrapper for LLM chat completion with centralized queueing.
    Prevents GGML_ASSERT failures by using isolated LLM processes.

    Uses centralized queue for intelligent routing, automatic retry, and recovery.
    Requests are queued and processed when LLM processes become available.
    """
    # Prepare request for the centralized queue
    request_data = {
        "action": "chat_completion",
        "messages": messages,
        "max_tokens": max_tokens,
        "temperature": temperature
    }

    try:
        # Submit to centralized queue and wait for result
        response = await centralized_queue.submit_chat_request(request_data)
        return response
    except Exception as e:
        logger.error(f"Error in centralized chat completion: {e}")
        raise RuntimeError(f"Chat completion failed: {str(e)}")

async def safe_embedding_llm_create_embedding(texts):
    """
    Process-safe wrapper for embedding LLM with centralized queueing.
    Prevents GGML_ASSERT failures by using isolated embedding process.

    Uses centralized queue for automatic retry and recovery.
    Requests are queued and processed when embedding process becomes available.
    """
    # Prepare request for the centralized queue
    request_data = {
        "action": "embedding",
        "texts": texts
    }

    try:
        # Submit to centralized queue and wait for result
        response = await centralized_queue.submit_embedding_request(request_data)
        return response
    except Exception as e:
        logger.error(f"Error in centralized embedding: {e}")
        raise RuntimeError(f"Embedding failed: {str(e)}")

# --- Helper Functions for Health Check ---
async def check_llm_availability():
    """
    Check which LLM processes are available for processing.
    Returns tuple: (main_available, backup_available)
    """
    main_process = llm_process_manager.get_process("main")
    backup_process = llm_process_manager.get_process("backup")

    main_available = main_process is not None and main_process.is_available()
    backup_available = backup_process is not None and backup_process.is_available()

    return main_available, backup_available

# --- Health Check (health_check) ---

@app.get("/api/v1/health")
async def health_check():
    """
    Why: Provide a basic health check for the service with process status.
    """
    logger.debug("Health check requested.")

    # Get process statuses
    main_llm_status = llm_process_manager.get_process_status("main")
    backup_llm_status = llm_process_manager.get_process_status("backup")
    embedding_llm_status = llm_process_manager.get_process_status("embedding")

    # Convert status to health check format
    def status_to_health(status):
        if status == "healthy":
            return "process_healthy"
        elif status in ["starting", "restarting"]:
            return "process_starting"
        elif status == "crashed":
            return "process_crashed"
        elif status == "stopped":
            return "process_stopped"
        else:
            return "not_found"

    main_llm_health = status_to_health(main_llm_status)
    backup_llm_health = status_to_health(backup_llm_status)
    embedding_llm_health = status_to_health(embedding_llm_status)

    qdrant_status = "initialized" if qdrant_client_instance else "not_initialized_or_error"

    # Check current availability
    main_available, backup_available = await check_llm_availability()

    return {
        "status": "ok",
        "main_llm_status": main_llm_health,
        "backup_llm_status": backup_llm_health,
        "embedding_llm_status": embedding_llm_health,
        "qdrant_status": qdrant_status,
        "main_llm_available": main_available,
        "backup_llm_available": backup_available,
        "concurrent_capacity": sum([main_available, backup_available]),
        "backup_llm_enabled": settings.backup_llm_enabled,
        "main_llm_model_configured": str(settings.absolute_models_dir / settings.model_filename),
        "backup_llm_model_configured": str(settings.absolute_models_dir / settings.backup_model_filename),
        "embedding_model_configured": str(settings.absolute_models_dir / settings.embedding_model_filename),
        "qdrant_path_configured": str(settings.absolute_qdrant_path)
    }

if __name__ == "__main__":
    import uvicorn
    logger.info(f"Starting Uvicorn server on {settings.app_host}:{settings.app_port}")
    uvicorn.run(
        "securamind_core.main:app",
        host=settings.app_host,
        port=settings.app_port,
        log_level=settings.log_level.lower(),
    )
