"""
LLM Worker Process Functions

This module contains the worker process functions that run the actual LLM models
with direct response routing for perfect stream isolation.
"""

import logging
import multiprocessing as mp
import queue
import time
import threading
from typing import Dict, Any

logger = logging.getLogger(__name__)


def llm_worker_process(
    config,  # LLMProcessConfig
    request_queue: mp.Queue,
    response_queue: mp.Queue,
    health_queue: mp.Queue
):
    """
    Main worker process that runs the LLM with direct response routing.
    
    This process:
    1. Loads the LLM model
    2. Processes requests with unique request IDs
    3. Routes responses directly to prevent cross-contamination
    4. Maintains health monitoring
    """
    try:
        # Import llama_cpp inside the worker process to avoid import issues
        from llama_cpp import Llama
        
        logger.info(f"Loading LLM model: {config.name}")
        
        # Configure LLM parameters
        llm_config = {
            "model_path": config.model_path,
            "n_ctx": config.n_ctx,
            "n_gpu_layers": config.n_gpu_layers,
            "verbose": config.verbose
        }

        # Add embedding flag for embedding models
        if config.process_type == "embedding":
            llm_config["embedding"] = True

        # Load the model
        llm = Llama(**llm_config)
        
        # Signal successful startup
        health_queue.put("ready")
        logger.info(f"LLM worker {config.name} is ready")
        
        # Start health monitoring thread
        health_thread = threading.Thread(
            target=_health_monitor_thread, 
            args=(health_queue,), 
            daemon=True
        )
        health_thread.start()
        
        # Main request processing loop
        _process_requests(llm, config, request_queue, response_queue)
        
    except Exception as e:
        logger.error(f"Fatal error in LLM worker {config.name}: {e}", exc_info=True)
        health_queue.put(f"error: {str(e)}")


def _health_monitor_thread(health_queue: mp.Queue):
    """Background thread that sends periodic health signals"""
    while True:
        try:
            health_queue.put("alive", timeout=1)
            time.sleep(10)  # Send health signal every 10 seconds
        except:
            break


def _process_requests(llm, config, request_queue: mp.Queue, response_queue: mp.Queue):
    """Main request processing loop"""
    while True:
        try:
            # Get next request (with timeout to allow health checks)
            request = request_queue.get(timeout=1)
            
            # Handle shutdown signal
            if request.get("action") == "shutdown":
                logger.info(f"LLM worker {config.name} received shutdown signal")
                break
            
            # Validate request has ID for routing
            request_id = request.get("request_id")
            if not request_id:
                logger.error("Received request without request_id")
                continue
            
            # Route request to appropriate handler
            if request.get("action") == "chat_completion":
                _handle_chat_completion(llm, request, request_id, response_queue, config.name)
            elif request.get("action") == "embedding":
                _handle_embedding(llm, request, request_id, response_queue, config.name)
            else:
                _send_response(request_id, response_queue, {
                    "error": f"Unknown action: {request.get('action')}"
                })
                
        except queue.Empty:
            continue  # Timeout - continue to allow health checks
        except Exception as e:
            logger.error(f"Error processing request in worker {config.name}: {e}", exc_info=True)


def _handle_chat_completion(llm, request: Dict[str, Any], request_id: str, response_queue: mp.Queue, worker_name: str):
    """Handle streaming chat completion requests"""
    try:
        # Create streaming completion
        stream = llm.create_chat_completion(
            messages=request["messages"],
            max_tokens=request.get("max_tokens", 1024),
            temperature=request.get("temperature", 0.7),
            stream=True
        )

        # Send stream start signal
        _send_response(request_id, response_queue, {"stream_start": True})

        # Process and send each token
        for chunk in stream:
            if "choices" in chunk and chunk["choices"]:
                delta = chunk["choices"][0].get("delta", {})
                content = delta.get("content")
                if content:
                    _send_response(request_id, response_queue, {"token": content})

        # Send stream end signal
        _send_response(request_id, response_queue, {"stream_end": True})

    except Exception as e:
        logger.error(f"Error in chat completion for worker {worker_name}: {e}", exc_info=True)
        _send_response(request_id, response_queue, {"error": f"Streaming error: {str(e)}"})


def _handle_embedding(llm, request: Dict[str, Any], request_id: str, response_queue: mp.Queue, worker_name: str):
    """Handle embedding requests"""
    try:
        # Generate embeddings
        response = llm.create_embedding(request["texts"])
        
        # Ensure response is in dict format
        if not isinstance(response, dict):
            response = {"data": response}
            
        _send_response(request_id, response_queue, response)
        
    except Exception as e:
        logger.error(f"Error in embedding for worker {worker_name}: {e}", exc_info=True)
        _send_response(request_id, response_queue, {"error": f"Embedding error: {str(e)}"})


def _send_response(request_id: str, response_queue: mp.Queue, response: Dict[str, Any]):
    """Send response with request ID for routing"""
    response["request_id"] = request_id
    response_queue.put(response)
