# securamind_core/config.py

# --- Configuration using Pydantic BaseSettings ---
import logging
from pydantic_settings import BaseSettings
from pathlib import Path

def setup_logging(log_level_str: str = "INFO"):
    """
    Why: To configure standardized logging for the application.
    How: Sets up a basic console handler with a formatter.
    """
    numeric_level = getattr(logging, log_level_str.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f'Invalid log level: {log_level_str}')

    logging.basicConfig(
        level=numeric_level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )

class Settings(BaseSettings):
    app_host: str = "127.0.0.1"
    app_port: int = 8000
    log_level: str = "INFO"

    # Path Configuration
    qdrant_path: str = "qdrant_data"
    models_dir: str = "models"

    # Main LLM Configuration
    model_filename: str = "gemma-3-4b-it-Q4_K_M.gguf"
    n_ctx: int = 4096
    max_tokens_default: int = 1024
    temperature_default: float = 0.7
    top_k_retrieval_default: int = 2
    n_gpu_layers: int = -1 #TODO: Does not seem to be used. Why?
    verbose_llm: bool = False

    #TODO
    #Please run a test:
#Send 100 API requests at the same moment (20 ingestions and 80 inferences) and make sure that it does not crash the server and all 100 users get a response eventually (long time of course)
#Log the tokens and responses that the 100 requests get as they come

#We should see the following:

#1. Infernce User 1 and 2 should recieve response tokens roughly equally soon and from then on more tokens will follow foot.
#2. 3-80 will recieve tokens later (except for the connection established response wich will happen right away)
#3. All ingestion users will recieve an ingestionIDs right that can be used to request the status of the ingestion. (we should make a handful of these status requests while the process is running, just to check)
#4. after one of the inference requests were finished it allows one of the other requests to start inference and their tokens are streamed etc
#5. ingestions happen more or less at the same time as they are chunked, right? What I mean is that all take turns instead of a fixed queue. (thats at least how it was before)
#6. If any model is down, it will simply not get scheduled until it was automaticly restarted

# TODO Notes: Run a speed test. It looks like the model have gotten slower?

    # Backup LLM Configuration
    backup_llm_enabled: bool = True
    backup_model_filename: str = "gemma-3-1b-it-Q4_K_M.gguf"
    backup_n_ctx: int = 2048
    backup_n_gpu_layers: int = -1
    backup_verbose_llm: bool = False

    # Embedding Model Configuration
    embedding_model_filename: str = "user-bge-m3-q8_0.gguf"
    embedding_model_n_gpu_layers: int = -1
    embedding_model_verbose: bool = False
    vector_dimension: int = 1024


    @property
    def absolute_models_dir(self) -> Path:
        project_root = Path(__file__).resolve().parent.parent
        return project_root / self.models_dir

    @property
    def absolute_qdrant_path(self) -> Path:
        project_root = Path(__file__).resolve().parent.parent
        return project_root / self.qdrant_path

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = 'ignore'

# Instance of settings to be imported by other modules
settings = Settings()